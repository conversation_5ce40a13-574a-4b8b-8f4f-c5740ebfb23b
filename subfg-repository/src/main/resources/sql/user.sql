CREATE TABLE `user` (
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID（主键）',
    `email` VARCHAR(100) NULL COMMENT '邮箱',
    `phone` BIGINT NULL COMMENT '手机号',
    `password` VARCHAR(255) NULL COMMENT '密码（MD5加密）',
    `salt` VARCHAR(64) NULL COMMENT '密码盐值',
    `role` VARCHAR(50) NULL COMMENT '用户角色',
    `user_name` VARCHAR(100) NULL COMMENT '用户名',
    `avatar_url` VARCHAR(500) NULL COMMENT '头像URL',
    `description` TEXT NULL COMMENT '个人描述',
    `credit_score` DECIMAL(10,2) NULL COMMENT '信用分数',
    `last_login_ip` VARCHAR(45) NULL COMMENT '最后登录IP地址',
    `last_login_time` BIGINT NULL COMMENT '最后登录时间',
    `register_ip` VARCHAR(45) NULL COMMENT '注册时的IP地址',
    `enable` TINYINT(1) NULL DEFAULT 1 COMMENT '是否启用（1:启用 0:禁用）',
    `create_time` BIGINT NULL COMMENT '创建时间',
    `update_time` BIGINT NULL COMMENT '更新时间',
    PRIMARY KEY (`user_id`),
    UNIQUE KEY `uk_email` (`email`),
    UNIQUE KEY `uk_phone` (`phone`),
    KEY `idx_user_name` (`user_name`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户表';