-- 三方登录绑定表
CREATE TABLE `user_third_party` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` VARCHAR(64) NOT NULL COMMENT '用户ID',
    `platform_type` VARCHAR(20) NOT NULL COMMENT '三方平台类型（wechat/qq/google/github等）',
    `platform_user_id` VARCHAR(100) NOT NULL COMMENT '三方平台用户唯一标识',
    `platform_username` VARCHAR(100) NULL COMMENT '三方平台用户名/昵称',
    `platform_avatar` VARCHAR(500) NULL COMMENT '三方平台头像URL',
    `union_id` VARCHAR(100) NULL COMMENT '联合ID（如微信unionid）',
    `access_token` TEXT NULL COMMENT '访问令牌（加密存储）',
    `refresh_token` TEXT NULL COMMENT '刷新令牌（加密存储）',
    `expires_time` BIGINT NULL COMMENT 'token过期时间',
    `bind_time` BIGINT NOT NULL COMMENT '绑定时间',
    `last_login_time` BIGINT NULL COMMENT '最后登录时间',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态（1:正常 0:禁用）',
    `create_time` BIGINT NOT NULL COMMENT '创建时间',
    `update_time` BIGINT NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_platform_user` (`platform_type`, `platform_user_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_platform_type` (`platform_type`),
    CONSTRAINT `fk_user_third_party_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户三方登录绑定表';