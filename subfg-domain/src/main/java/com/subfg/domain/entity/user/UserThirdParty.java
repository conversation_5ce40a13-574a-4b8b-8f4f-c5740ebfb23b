package com.subfg.domain.entity.user;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.subfg.domain.enums.ThirdPlatformType;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户三方登录绑定实体类
 * 对应数据库表：user_third_party
 * 
 * <AUTHOR>
 * @since 2025-01-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_third_party")
public class UserThirdParty implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 三方平台类型（wechat/qq/google/github等）
     */
    @TableField("platform_type")
    private String platformType;

    /**
     * 三方平台用户唯一标识（微信使用unionid，其他平台使用对应的唯一ID）
     */
    @TableField("platform_user_id")
    private String platformUserId;

    /**
     * 三方平台用户名/昵称
     */
    @TableField("platform_username")
    private String platformUsername;

    /**
     * 三方平台头像URL
     */
    @TableField("platform_avatar")
    private String platformAvatar;

    /**
     * 访问令牌（加密存储）
     */
    @TableField("access_token")
    private String accessToken;

    /**
     * 刷新令牌（加密存储）
     */
    @TableField("refresh_token")
    private String refreshToken;

    /**
     * token过期时间
     */
    @TableField("expires_time")
    private Long expiresTime;

    /**
     * 绑定时间
     */
    @TableField("bind_time")
    private Long bindTime;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private Long lastLoginTime;

    /**
     * 状态（1:正常 0:禁用）
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 获取平台类型枚举
     *
     * @return 平台类型枚举
     */
    public ThirdPlatformType getPlatformTypeEnum() {
        return ThirdPlatformType.fromCode(this.platformType);
    }

    /**
     * 设置平台类型枚举
     *
     * @param platformType 平台类型枚举
     */
    public void setPlatformTypeEnum(ThirdPlatformType platformType) {
        this.platformType = platformType != null ? platformType.getCode() : null;
    }

    /**
     * 检查token是否过期
     *
     * @return 是否过期
     */
    public boolean isTokenExpired() {
        return expiresTime != null && System.currentTimeMillis() > expiresTime;
    }

    /**
     * 检查是否启用
     *
     * @return 是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }
}
