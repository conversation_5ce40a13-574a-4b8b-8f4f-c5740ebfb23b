package com.subfg.domain.entity.fg;

import java.io.Serializable;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 家庭组订阅信息实体类
 * 对应数据库表：fg_substore
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fg_substore")
public class FgSubstore implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 家庭组ID（主键）
     */
    @TableId(value = "family_group_id", type = IdType.INPUT)
    private String familyGroupId;

    /**
     * 家庭组名称
     */
    @TableField("family_group_name")
    private String familyGroupName;

    /**
     * 地区ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Integer productId;

    /**
     * 套餐ID
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 创建家庭组用户ID
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 团长ID 如果家庭组类型为自建则创建用户为团长
     */
    @TableField("leader_id")
    private String leaderId;

    /**
     * 家庭组状态(-2:不能审核 -1: 未通过 0 审核中 1: 审核通过,组建中  2: 车队已满,  3: 车队暂时不可加入 4:过期 5: 关闭)
     */
    @TableField("family_group_status")
    private Integer familyGroupStatus;

    /**
     * 家庭组类型[1.自建 2.拼团]
     */
    @TableField("family_group_type")
    private Integer familyGroupType;

    /**
     * 家庭组加入人数
     */
    @TableField("family_group_join_count")
    private Integer familyGroupJoinCount;

    /**
     * 总空位数
     */
    @TableField("sum_vacancy")
    private Integer sumVacancy;

    /**
     * 计费周期
     */
    @TableField("billing_cycle")
    private Integer billingCycle;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

    /**
     * 最新加入时间
     */
    @TableField("latest_join_time")
    private Long latestJoinTime;

    /**
     * 服务过期时间
     */
    @TableField("service_expire_time")
    private Long serviceExpireTime;

    /**
     * 审核图片
     */
    @TableField("review_picture")
    private String reviewPicture;

    /**
     * 第三方详情
     */
    @TableField("third_detail")
    private String thirdDetail;

    /**
     * 原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 审核创建时间
     */
    @TableField("review_create_time")
    private Long reviewCreateTime;

    /**
     * 审核时间
     */
    @TableField("review_time")
    private Long reviewTime;

    /**
     * 审核用户ID
     */
    @TableField("review_user_id")
    private String reviewUserId;

}
